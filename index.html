<style>
/* Reset and scope all styles to avoid Elementor conflicts */
.image-converter-widget * {
    box-sizing: border-box;
}

.image-converter-widget {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Arial, sans-serif;
    margin: 0;
    padding: 15px;
    background-color: #ffffff;
    color: #333;
    line-height: 1.6;
    width: 100%;
    min-height: 100vh;
}

/* Main Container */
.image-converter-widget .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 15px;
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Headings - Responsive sizing */
.image-converter-widget h1 {
    color: #2196f3;
    margin-top: 0;
    margin-bottom: 15px;
    font-size: clamp(1.5rem, 4vw, 2.5rem);
    text-align: center;
}

.image-converter-widget h2, 
.image-converter-widget h3, 
.image-converter-widget h4, 
.image-converter-widget h5, 
.image-converter-widget h6 {
    color: #2196f3;
    margin-top: 0;
    margin-bottom: 15px;
    font-size: clamp(1.1rem, 3vw, 1.5rem);
}

/* Buttons - Enhanced responsive design */
.image-converter-widget button {
    background-color: #2196f3;
    color: white;
    border: none;
    padding: 12px 16px;
    border-radius: 5px;
    cursor: pointer;
    font-size: clamp(14px, 2.5vw, 16px);
    transition: all 0.3s ease;
    min-height: 44px; /* Touch-friendly minimum */
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    white-space: nowrap;
}

.image-converter-widget button:hover {
    background-color: #1976d2;
    transform: translateY(-1px);
}

.image-converter-widget button:not(.align-btn):not(.icon-button) {
    background-color: #2196f3;
}

/* Separators */
.image-converter-widget hr {
    border: none;
    border-top: 1px solid #e0e0e0;
    margin: 20px 0;
}

/* Input Fields - Enhanced responsive design */
.image-converter-widget input[type="text"],
.image-converter-widget input[type="number"],
.image-converter-widget textarea,
.image-converter-widget select {
    width: 100%;
    padding: 12px;
    margin-bottom: 15px;
    border: 1px solid #e0e0e0;
    border-radius: 5px;
    box-sizing: border-box;
    font-size: clamp(14px, 2.5vw, 16px);
    min-height: 44px; /* Touch-friendly */
}

/* Layout Breakpoints - Mobile First Approach */
/* Mobile (default) - up to 767px */
.image-converter-widget .main-content {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.image-converter-widget .left-panel,
.image-converter-widget .right-panel {
    width: 100%;
}

.image-converter-widget .right-panel {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

/* Tablet - 768px to 1023px */
@media (min-width: 768px) {
    .image-converter-widget {
        padding: 20px;
    }
    
    .image-converter-widget .container {
        padding: 20px;
    }
    
    .image-converter-widget .main-content {
        gap: 20px;
    }
    
    .image-converter-widget .right-panel {
        gap: 15px;
    }
    
    /* Two-column layout for tablets in landscape */
    @media (orientation: landscape) and (min-width: 1024px) {
        .image-converter-widget .main-content {
            flex-direction: row;
        }
        
        .image-converter-widget .left-panel {
            flex: 1;
            min-width: 300px;
        }
        
        .image-converter-widget .right-panel {
            flex: 2;
            min-width: 400px;
        }
    }
}

/* Desktop - 1024px and up */
@media (min-width: 1024px) {
    .image-converter-widget .main-content {
        flex-direction: row;
        gap: 25px;
    }

    .image-converter-widget .left-panel {
        flex: 1;
        min-width: 320px;
        max-width: 400px;
    }

    .image-converter-widget .right-panel {
        flex: 2;
        min-width: 500px;
        gap: 20px;
    }

    .image-converter-widget .right-panel #live-preview-section {
        flex: 1;
        min-height: 300px;
    }
    
    .image-converter-widget .right-panel #preview-output-section {
        flex: 2;
        min-height: 400px;
    }
}

/* Large Desktop - 1400px and up */
@media (min-width: 1400px) {
    .image-converter-widget .main-content {
        gap: 30px;
    }
    
    .image-converter-widget .left-panel {
        max-width: 450px;
    }
}

/* Utility Classes */
.image-converter-widget .section-title {
    font-size: clamp(1.2rem, 3vw, 1.5rem);
    margin-bottom: 15px;
    color: #2196f3;
    text-align: center;
}

@media (min-width: 768px) {
    .image-converter-widget .section-title {
        text-align: left;
    }
}

.image-converter-widget .panel {
    background-color: #f9f9f9;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    width: 100%;
}

@media (min-width: 768px) {
    .image-converter-widget .panel {
        padding: 20px;
        margin-bottom: 20px;
    }
}

/* Upload Section Styles - Enhanced responsive */
.image-converter-widget .upload-content {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

@media (min-width: 768px) {
    .image-converter-widget .upload-content {
        gap: 20px;
    }
}

.image-converter-widget .upload-content .input-group {
    flex: 1;
}

.image-converter-widget .drop-zone {
    border: 2px dashed #2196f3;
    border-radius: 8px;
    padding: 20px 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 15px;
    min-height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
}

@media (min-width: 768px) {
    .image-converter-widget .drop-zone {
        padding: 30px 20px;
        min-height: 150px;
    }
}

.image-converter-widget .drop-zone.hover {
    background-color: #e3f2fd;
    transform: scale(1.02);
}

.image-converter-widget .drop-zone input[type="file"] {
    display: none;
}

.image-converter-widget .drop-zone p {
    margin: 0;
    font-size: clamp(14px, 2.5vw, 16px);
    color: #2196f3;
    font-weight: 500;
}

.image-converter-widget .input-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    font-size: clamp(14px, 2.5vw, 16px);
}

.image-converter-widget .preview-container {
    margin-top: 20px;
    text-align: center;
    border: 1px dashed #e0e0e0;
    padding: 15px;
    border-radius: 5px;
}

.image-converter-widget #canvasContainer {
    overflow: auto;
    border: 1px solid #ccc;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 200px;
    margin-bottom: 10px;
    border-radius: 5px;
    background: #f8f8f8;
}

@media (min-width: 768px) {
    .image-converter-widget #canvasContainer {
        height: 250px;
    }
}

@media (min-width: 1024px) {
    .image-converter-widget #canvasContainer {
        height: 300px;
    }
}

.image-converter-widget #imagePreviewCanvas {
    max-width: 100%;
    height: auto;
    display: block;
    transform-origin: center center;
    transition: transform 0.1s ease-out;
    border-radius: 3px;
}

/* Customization Panel Styles - Enhanced responsive */
.image-converter-widget .customization-options .option-group {
    margin-bottom: 20px;
}

.image-converter-widget .customization-options label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    font-size: clamp(14px, 2.5vw, 16px);
}

.image-converter-widget .customization-options select,
.image-converter-widget .customization-options input[type="number"] {
    width: 100%;
    padding: 12px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    font-size: clamp(14px, 2.5vw, 16px);
    min-height: 44px;
}

/* Grid layout for number inputs on larger screens */
@media (min-width: 768px) {
    .image-converter-widget .size-inputs {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 15px;
    }
}

.image-converter-widget .alignment-options {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.image-converter-widget .alignment-buttons {
    display: flex;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 10px;
    gap: 5px;
}

.image-converter-widget .alignment-buttons .align-btn {
    background-color: #f0f0f0;
    border: 1px solid #ccc;
    padding: 8px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    min-height: 44px;
    transition: all 0.2s ease;
}

.image-converter-widget .alignment-buttons .align-btn svg {
    fill: #333;
    width: 20px;
    height: 20px;
}

@media (max-width: 480px) {
    .image-converter-widget .alignment-buttons .align-btn svg {
        width: 16px;
        height: 16px;
    }
}

.image-converter-widget .alignment-buttons .align-btn.active {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
}

.image-converter-widget .alignment-buttons .align-btn.active svg {
    fill: white;
}

.image-converter-widget .customization-options input[type="range"] {
    width: 100%;
    -webkit-appearance: none;
    height: 8px;
    background: #e0e0e0;
    border-radius: 5px;
    outline: none;
    opacity: 0.7;
    transition: opacity .2s;
    margin: 10px 0;
}

.image-converter-widget .customization-options input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 24px;
    height: 24px;
    background: #2196f3;
    border-radius: 50%;
    cursor: pointer;
}

.image-converter-widget .customization-options input[type="range"]::-moz-range-thumb {
    width: 24px;
    height: 24px;
    background: #2196f3;
    border-radius: 50%;
    cursor: pointer;
    border: none;
}

.image-converter-widget .customization-options input[type="checkbox"] {
    margin-right: 10px;
    width: auto;
    min-height: auto;
}

/* Output Section Styles - Enhanced responsive */
.image-converter-widget .output-code-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 15px;
    background-color: #F9F9F9;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    color: #2196f3;
    flex-wrap: wrap;
    gap: 10px;
}

@media (max-width: 480px) {
    .image-converter-widget .output-code-header {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }
}

.image-converter-widget .output-code-header label {
    font-weight: bold;
    font-size: clamp(14px, 2.5vw, 16px);
    margin-bottom: 5px;
}

.image-converter-widget .output-code-header select {
    min-width: 150px;
    flex: 1;
    max-width: 200px;
}

@media (max-width: 480px) {
    .image-converter-widget .output-code-header select {
        max-width: none;
        width: 100%;
    }
}

.image-converter-widget .code-box-container {
    position: relative;
}

.image-converter-widget #generatedCode {
    width: 100%;
    min-height: 250px;
    resize: vertical;
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
    font-size: clamp(12px, 2vw, 14px);
    background-color: #2d2d2d;
    color: #f8f8f2;
    border: 1px solid #e0e0e0;
    border-top: none;
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    padding: 15px;
    box-sizing: border-box;
    overflow: auto;
    line-height: 1.4;
}

@media (min-width: 768px) {
    .image-converter-widget #generatedCode {
        min-height: 300px;
    }
}

@media (min-width: 1024px) {
    .image-converter-widget #generatedCode {
        min-height: 350px;
    }
}

.image-converter-widget .output-buttons {
    display: flex;
    gap: 8px;
    margin-left: auto;
    flex-wrap: wrap;
}

@media (max-width: 480px) {
    .image-converter-widget .output-buttons {
        margin-left: 0;
        width: 100%;
        justify-content: center;
    }
}

.image-converter-widget .output-buttons button {
    background-color: #2196f3;
    color: white;
    border: none;
    padding: 10px 12px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    min-width: 44px;
    min-height: 44px;
}

.image-converter-widget .output-buttons button:hover {
    background-color: #1976d2;
    transform: translateY(-1px);
}

.image-converter-widget .output-buttons svg {
    width: 20px;
    height: 20px;
    stroke: white;
}

/* Icon buttons - Enhanced responsive grid */
.image-converter-widget .icon-button-group {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
    margin-top: 10px;
}

@media (max-width: 480px) {
    .image-converter-widget .icon-button-group {
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
    }
}

@media (min-width: 768px) {
    .image-converter-widget .icon-button-group {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
    }
}

@media (min-width: 1024px) {
    .image-converter-widget .icon-button-group {
        grid-template-columns: repeat(4, 1fr);
        gap: 10px;
    }
}

.image-converter-widget .icon-button {
    background-color: #f0f0f0;
    border: 1px solid #ccc;
    border-radius: 5px;
    padding: 10px 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    font-size: clamp(12px, 2vw, 14px);
    color: #333;
    transition: all 0.2s ease;
    min-height: 44px;
    text-align: center;
    white-space: nowrap;
}

@media (max-width: 480px) {
    .image-converter-widget .icon-button {
        flex-direction: column;
        gap: 4px;
        padding: 8px 4px;
    }
    
    .image-converter-widget .icon-button span {
        font-size: 11px;
    }
}

.image-converter-widget .icon-button:hover {
    background-color: #e0e0e0;
    border-color: #999;
    transform: translateY(-1px);
}

.image-converter-widget .icon-button.active {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
}

.image-converter-widget .icon-button.active svg {
    stroke: white;
}

.image-converter-widget .icon-button svg {
    width: 18px;
    height: 18px;
    stroke: #333;
    stroke-width: 2;
    fill: none;
    flex-shrink: 0;
}

@media (max-width: 480px) {
    .image-converter-widget .icon-button svg {
        width: 16px;
        height: 16px;
    }
}

/* Zoom controls - Enhanced responsive */
.image-converter-widget .zoom-controls {
    display: flex;
    gap: 8px;
    margin-top: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.image-converter-widget .zoom-controls button {
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 5px;
    padding: 10px 15px;
    cursor: pointer;
    font-size: clamp(12px, 2vw, 14px);
    transition: all 0.2s ease;
    min-height: 44px;
    flex: 1;
    max-width: 120px;
}

@media (max-width: 480px) {
    .image-converter-widget .zoom-controls {
        gap: 6px;
    }
    
    .image-converter-widget .zoom-controls button {
        padding: 8px 12px;
        font-size: 12px;
    }
}

.image-converter-widget .zoom-controls button:hover {
    background-color: #0056b3;
    transform: translateY(-1px);
}

/* Message area - Enhanced responsive */
.image-converter-widget #message-area {
    padding: 12px 15px;
    margin-bottom: 20px;
    border-radius: 5px;
    text-align: center;
    font-weight: bold;
    display: none;
    border: 1px solid;
    font-size: clamp(14px, 2.5vw, 16px);
    word-wrap: break-word;
}

/* SEO Content Styles - Enhanced responsive */
.image-converter-widget #seo-content {
    margin-top: 30px;
}

.image-converter-widget #seo-content h3 {
    color: #2196f3;
    margin-top: 25px;
    margin-bottom: 15px;
    font-size: clamp(1.1rem, 3vw, 1.3rem);
}

.image-converter-widget #seo-content p,
.image-converter-widget #seo-content li {
    margin-bottom: 12px;
    text-align: justify;
    font-size: clamp(14px, 2.5vw, 16px);
    line-height: 1.6;
}

@media (max-width: 768px) {
    .image-converter-widget #seo-content p,
    .image-converter-widget #seo-content li {
        text-align: left;
    }
}

.image-converter-widget #seo-content ol,
.image-converter-widget #seo-content ul {
    margin-left: 20px;
    padding-left: 0;
}

@media (max-width: 480px) {
    .image-converter-widget #seo-content ol,
    .image-converter-widget #seo-content ul {
        margin-left: 15px;
    }
}

.image-converter-widget #seo-content li {
    list-style-type: disc;
    margin-left: 20px;
}

.image-converter-widget #seo-content ol li {
    list-style-type: decimal;
}

/* Enhanced focus states for accessibility */
.image-converter-widget button:focus,
.image-converter-widget input:focus,
.image-converter-widget select:focus,
.image-converter-widget textarea:focus {
    outline: 2px solid #2196f3;
    outline-offset: 2px;
}

/* Smooth scrolling for better UX */
.image-converter-widget {
    scroll-behavior: smooth;
}

/* Loading states */
.image-converter-widget .loading {
    opacity: 0.7;
    pointer-events: none;
}

/* Print styles */
@media print {
    .image-converter-widget {
        padding: 0;
    }
    
    .image-converter-widget .zoom-controls,
    .image-converter-widget .output-buttons,
    .image-converter-widget button {
        display: none !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .image-converter-widget {
        border: 2px solid;
    }
    
    .image-converter-widget button {
        border: 2px solid;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .image-converter-widget * {
        transition: none !important;
        animation: none !important;
    }
}

/* Container queries for modern browsers */
@supports (container-type: inline-size) {
    .image-converter-widget .container {
        container-type: inline-size;
    }
    
    @container (max-width: 600px) {
        .image-converter-widget .main-content {
            flex-direction: column;
        }
    }
}
</style>

<div class="image-converter-widget">
    <div id="message-area"></div>
    <div class="container">
        <h1>Image to C++ Code Converter</h1>
        
        <!-- Upload Section -->
        <section id="upload-section" class="panel">
            <h2 class="section-title">Upload Image or Paste Byte Array</h2>
            <div class="upload-content">
                <div class="drop-zone" id="dropZone">
                    <p>Drag & Drop Image Here or Click to Upload</p>
                    <input type="file" id="imageUpload" accept=".png,.jpg,.jpeg,.bmp,.gif">
                </div>
                <div class="input-group">
                    <label for="byteArrayPaste">OR Paste Byte Array:</label>
                    <textarea id="byteArrayPaste" rows="5" placeholder="e.g., 0x01, 0x02, 0xFF..."></textarea>
                </div>
            </div>
        </section>

        <hr>

        <div class="main-content">
            <!-- Left Panel -->
            <aside class="left-panel">
                <section id="customization-panel" class="panel">
                    <h2 class="section-title">Customization Options</h2>
                    <div class="customization-options">
                        <div class="option-group">
                            <label for="canvasWidth">Canvas Width (px):</label>
                            <input type="number" id="canvasWidth" value="128" min="1">
                        </div>
                        <div class="option-group">
                            <label for="canvasHeight">Canvas Height (px):</label>
                            <input type="number" id="canvasHeight" value="64" min="1">
                        </div>
                        <div class="option-group">
                            <label for="fitMode">Fit Mode:</label>
                            <select id="fitMode">
                                <option value="fill">Fill</option>
                                <option value="fit">Fit</option>
                                <option value="stretch">Stretch</option>
                                <option value="center">Center</option>
                            </select>
                        </div>
                        <div class="option-group">
                            <label>Alignment:</label>
                            <div class="alignment-options">
                                <div class="alignment-buttons" id="alignH">
                                    <button class="align-btn" data-value="left"><svg viewBox="0 0 24 24" width="24" height="24"><path d="M3 19h18v2H3v-2zm0-6h18v2H3v-2zm0-4h18v2H3V9zm0-4h18v2H3V5z"/></svg></button>
                                    <button class="align-btn" data-value="center"><svg viewBox="0 0 24 24" width="24" height="24"><path d="M7 19h10v2H7v-2zm-4-6h18v2H3v-2zm4-4h10v2H7V9zm-4-4h18v2H3V5z"/></svg></button>
                                    <button class="align-btn" data-value="right"><svg viewBox="0 0 24 24" width="24" height="24"><path d="M3 19h18v2H3v-2zm0-6h18v2H3v-2zm0-4h18v2H3V9zm0-4h18v2H3V5z"/></svg></button>
                                </div>
                                <div class="alignment-buttons" id="alignV">
                                    <button class="align-btn" data-value="top"><svg viewBox="0 0 24 24" width="24" height="24"><path d="M19 3v18h2V3h-2zm-6 0v18h2V3h-2zm-4 0v18h2V3H9zm-4 0v18h2V3H5z"/></svg></button>
                                    <button class="align-btn" data-value="middle"><svg viewBox="0 0 24 24" width="24" height="24"><path d="M19 7v10h2V7h-2zm-6-4v18h2V3h-2zm-4 4v10h2V7H9zm-4-4v18h2V3H5z"/></svg></button>
                                    <button class="align-btn" data-value="bottom"><svg viewBox="0 0 24 24" width="24" height="24"><path d="M19 3v18h2V3h-2zm-6 0v18h2V3h-2zm-4 0v18h2V3H9zm-4 0v18h2V3H5z"/></svg></button>
                                </div>
                            </div>
                        </div>
                        <div class="option-group">
                            <label for="brightnessThreshold">Brightness Threshold: <span id="thresholdValue">128</span></label>
                            <input type="range" id="brightnessThreshold" min="0" max="255" value="128">
                        </div>
                        <div class="option-group">
                            <label>Background:</label>
                            <select id="backgroundColor">
                                <option value="white">White</option>
                                <option value="black">Black</option>
                                <option value="transparent">Transparent</option>
                            </select>
                        </div>
                        
                        <div class="option-group">
                            <label>Image Adjustments:</label>
                            <div class="icon-button-group">
                                <button type="button" class="icon-button" id="invertColorsBtn">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path><line x1="12" y1="16" x2="12" y2="12"></line><line x1="12" y1="8" x2="12" y2="8"></line></svg>
                                    <span>Invert</span>
                                </button>
                                <button type="button" class="icon-button" id="mirrorHBtn">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 5v14M5 12h14M12 5l4 4M12 5l-4 4M12 19l4-4M12 19l-4-4M5 12l4-4M5 12l4 4M19 12l-4-4M19 12l-4 4"></path></svg>
                                    <span>Mirror H</span>
                                </button>
                                <button type="button" class="icon-button" id="mirrorVBtn">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 5v14M5 12h14M12 5l4 4M12 5l-4 4M12 19l4-4M12 19l-4-4M5 12l4-4M5 12l4 4M19 12l-4-4M19 12l-4 4" transform="rotate(90 12 12)"></path></svg>
                                    <span>Mirror V</span>
                                </button>
                                <button type="button" class="icon-button" id="enableDitheringBtn">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><path d="M12 6v6l4 2"></path></svg>
                                    <span>Dithering</span>
                                </button>
                            </div>
                        </div>

                        <div class="option-group" id="ditheringIntensityGroup" style="display: none;">
                            <label for="ditheringIntensity">Dithering Intensity: <span id="ditheringIntensityValue">50</span></label>
                            <input type="range" id="ditheringIntensity" min="0" max="100" value="50">
                        </div>
                    </div>
                </section>
            </aside>

            <!-- Right Panel -->
            <main class="right-panel">
                <section id="live-preview-section" class="panel">
                    <h2 class="section-title">Live Image Preview:</h2>
                    <div class="preview-container">
                        <div id="canvasContainer">
                            <canvas id="imagePreviewCanvas"></canvas>
                        </div>
                        <div class="zoom-controls">
                            <button id="zoomInBtn">Zoom In</button>
                            <button id="zoomOutBtn">Zoom Out</button>
                            <button id="resetZoomBtn">Reset Zoom</button>
                        </div>
                    </div>
                </section>
                
                <section id="preview-output-section" class="panel">
                    <div class="output-section">
                        <div class="code-box-container">
                            <div class="output-code-header">
                                <label for="outputFormatMoved">Output Format:</label>
                                <select id="outputFormatMoved">
                                    <option value="arduino_cpp">Arduino .cpp</option>
                                    <option value="arduino_h">Arduino .h</option>
                                    <option value="arduino_ino">Arduino .ino</option>
                                    <option value="json">JSON</option>
                                    <option value="hex_array">Hex Array</option>
                                    <option value="base64">Base64</option>
                                    <option value="pbm">PBM</option>
                                    <option value="plain_bytes">Plain Bytes</option>
                                    <option value="cpp_byte_array">C++ Byte Array</option>
                                    <option value="python_byte_array">Python Byte Array</option>
                                </select>
                                <div class="output-buttons">
                                    <button id="copyCodeBtn" title="Copy to Clipboard">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect><path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path></svg>
                                    </button>
                                    <button id="downloadCodeBtn" title="Download Code">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points="7 10 12 15 17 10"></polyline><line x1="12" y1="15" x2="12" y2="3"></line></svg>
                                    </button>
                                </div>
                            </div>
                            <textarea id="generatedCode" readonly rows="15"></textarea>
                        </div>
                    </div>
                </section>
            </main>
        </div>

        <hr>

        <!-- SEO Content -->
        <section id="seo-content" class="panel">
            <h2 class="section-title">Image to C++ Code Converter: Tutorial & FAQ</h2>
            <p>Welcome to the ultimate online tool for converting images into C++ code, perfect for embedding bitmaps into your Arduino projects, especially for OLED displays like the SSD1306. This <strong>image2cpp</strong> converter simplifies the process of transforming your PNG, JPG, BMP, or GIF images into various code formats, including Arduino-ready C/C++ arrays, JSON, Hex, Base64, PBM, and Plain Bytes.</p>

            <h3>How to Use the Tool (Step-by-Step)</h3>
            <ol>
                <li><strong>Upload Your Image:</strong> Drag and drop your image (PNG, JPG, BMP, or GIF) into the designated area, or click to select a file. Alternatively, paste an existing byte array directly into the provided textarea.</li>
                <li><strong>Live Previews:</strong> Observe two live previews: the "Live Image Preview" shows how your image will look on the target display, and the "Live Generated Preview Code" updates in real-time with the corresponding code.</li>
                <li><strong>Customize Your Output:</strong> Utilize the "Customization Options" panel to fine-tune your image conversion.</li>
                <li><strong>Select Output Format:</strong> Choose your desired code format from the dropdown menu.</li>
                <li><strong>Copy or Download Code:</strong> The generated code automatically appears in the code box. Easily copy it to your clipboard or download it as a file.</li>
            </ol>

            <h3>Understanding Output Formats</h3>
            <ul>
                <li><strong>Arduino .cpp / .h:</strong> These formats generate C++ arrays suitable for inclusion in Arduino sketches.</li>
                <li><strong>Arduino .ino:</strong> This option generates a complete Arduino sketch, ready to be uploaded to your board.</li>
                <li><strong>JSON:</strong> Outputs the image data in a structured JSON format.</li>
                <li><strong>Hex Array:</strong> Provides the raw byte data in hexadecimal format.</li>
                <li><strong>Base64:</strong> Encodes the image data into a Base64 string.</li>
                <li><strong>PBM:</strong> A simple, plain text image format that represents monochrome images.</li>
                <li><strong>Plain Bytes:</strong> Outputs the raw binary data as a comma-separated list.</li>
                <li><strong>C++ Byte Array:</strong> Generates a C++ array, ideal for direct embedding in C/C++ projects.</li>
                <li><strong>Python Byte Array:</strong> Provides the image data as a Python array.</li>
            </ul>
        </section>
    </div>
</div>

<script>
(function() {
    // Scope all variables and functions to avoid global conflicts
    const widget = document.querySelector('.image-converter-widget');
    const imageUpload = widget.querySelector('#imageUpload');
    const byteArrayPaste = widget.querySelector('#byteArrayPaste');
    const imagePreviewCanvas = widget.querySelector('#imagePreviewCanvas');
    const ctx = imagePreviewCanvas.getContext('2d');
    const dropZone = widget.querySelector('#dropZone');

    let originalImage = null;

    // Drag and Drop functionality
    dropZone.addEventListener('dragover', (e) => {
        e.preventDefault();
        dropZone.classList.add('hover');
    });

    dropZone.addEventListener('dragleave', () => {
        dropZone.classList.remove('hover');
    });

    dropZone.addEventListener('drop', (e) => {
        e.preventDefault();
        dropZone.classList.remove('hover');
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            imageUpload.files = files;
            imageUpload.dispatchEvent(new Event('change'));
        }
    });

    dropZone.addEventListener('click', () => {
        imageUpload.click();
    });

    function drawImageOnCanvas(img) {
        let canvasWidth = parseInt(widget.querySelector('#canvasWidth').value);
        let canvasHeight = parseInt(widget.querySelector('#canvasHeight').value);

        if (isNaN(canvasWidth) || canvasWidth <= 0) {
            showMessage('Canvas width must be a positive number.', 'error');
            canvasWidth = 128;
            widget.querySelector('#canvasWidth').value = canvasWidth;
        }
        if (isNaN(canvasHeight) || canvasHeight <= 0) {
            showMessage('Canvas height must be a positive number.', 'error');
            canvasHeight = 64;
            widget.querySelector('#canvasHeight').value = canvasHeight;
        }
        
        const fitMode = widget.querySelector('#fitMode').value;
        const alignH = widget.querySelector('#alignH .align-btn.active')?.dataset.value || 'left';
        const alignV = widget.querySelector('#alignV .align-btn.active')?.dataset.value || 'top';
        let brightnessThreshold = parseInt(widget.querySelector('#brightnessThreshold').value);
        
        if (isNaN(brightnessThreshold) || brightnessThreshold < 0 || brightnessThreshold > 255) {
            showMessage('Brightness threshold must be between 0 and 255.', 'error');
            brightnessThreshold = 128;
            widget.querySelector('#brightnessThreshold').value = brightnessThreshold;
        }

        const invertColors = widget.querySelector('#invertColorsBtn').classList.contains('active');
        const enableDithering = widget.querySelector('#enableDitheringBtn').classList.contains('active');
        let ditheringIntensity = parseInt(widget.querySelector('#ditheringIntensity').value);
        
        if (isNaN(ditheringIntensity) || ditheringIntensity < 0 || ditheringIntensity > 100) {
            showMessage('Dithering intensity must be between 0 and 100.', 'error');
            ditheringIntensity = 50;
            widget.querySelector('#ditheringIntensity').value = ditheringIntensity;
        }
        
        const backgroundColor = widget.querySelector('#backgroundColor').value;

        imagePreviewCanvas.width = canvasWidth;
        imagePreviewCanvas.height = canvasHeight;

        // Clear canvas with background color
        if (backgroundColor === 'transparent') {
            ctx.clearRect(0, 0, canvasWidth, canvasHeight);
        } else {
            ctx.fillStyle = backgroundColor;
            ctx.fillRect(0, 0, canvasWidth, canvasHeight);
        }

        let sx = 0, sy = 0, sWidth = img.width, sHeight = img.height;
        let dx = 0, dy = 0, dWidth = canvasWidth, dHeight = canvasHeight;

        // Apply fit mode logic
        if (fitMode === 'fit') {
            const aspectRatio = img.width / img.height;
            const canvasAspectRatio = canvasWidth / canvasHeight;

            if (aspectRatio > canvasAspectRatio) {
                dWidth = canvasWidth;
                dHeight = canvasWidth / aspectRatio;
            } else {
                dHeight = canvasHeight;
                dWidth = canvasHeight * aspectRatio;
            }
        } else if (fitMode === 'stretch') {
            // Already set to stretch by default
        } else if (fitMode === 'center') {
            dWidth = img.width;
            dHeight = img.height;
            if (dWidth > canvasWidth) {
                dHeight = dHeight * (canvasWidth / dWidth);
                dWidth = canvasWidth;
            }
            if (dHeight > canvasHeight) {
                dWidth = dWidth * (canvasHeight / dHeight);
                dHeight = canvasHeight;
            }
        }

        // Apply alignment logic
        if (alignH === 'center') {
            dx = (canvasWidth - dWidth) / 2;
        } else if (alignH === 'right') {
            dx = canvasWidth - dWidth;
        }

        if (alignV === 'middle') {
            dy = (canvasHeight - dHeight) / 2;
        } else if (alignV === 'bottom') {
            dy = canvasHeight - dHeight;
        }

        // Apply mirroring transformations
        const mirrorH = widget.querySelector('#mirrorHBtn').classList.contains('active');
        const mirrorV = widget.querySelector('#mirrorVBtn').classList.contains('active');

        ctx.save();

        if (mirrorH) {
            ctx.translate(canvasWidth, 0);
            ctx.scale(-1, 1);
            dx = canvasWidth - (dx + dWidth);
        }
        if (mirrorV) {
            ctx.translate(0, canvasHeight);
            ctx.scale(1, -1);
            dy = canvasHeight - (dy + dHeight);
        }

        ctx.drawImage(img, sx, sy, sWidth, sHeight, dx, dy, dWidth, dHeight);
        ctx.restore();

        // Get image data for pixel manipulation
        const imageData = ctx.getImageData(0, 0, canvasWidth, canvasHeight);
        const data = imageData.data;

        // Apply brightness threshold, invert, and dithering
        for (let i = 0; i < data.length; i += 4) {
            const r = data[i];
            const g = data[i + 1];
            const b = data[i + 2];

            // Convert to grayscale using luminance method
            let gray = 0.2126 * r + 0.7152 * g + 0.0722 * b;

            // Apply dithering if enabled
            if (enableDithering) {
                const oldPixel = gray;
                const newPixel = oldPixel < brightnessThreshold ? 0 : 255;
                const error = (oldPixel - newPixel) * (ditheringIntensity / 100);

                data[i] = data[i + 1] = data[i + 2] = newPixel;

                // Floyd-Steinberg dithering error diffusion
                const x = (i / 4) % canvasWidth;
                const y = Math.floor((i / 4) / canvasWidth);

                if (x + 1 < canvasWidth) {
                    data[i + 4] += error * (7 / 16);
                    data[i + 5] += error * (7 / 16);
                    data[i + 6] += error * (7 / 16);
                }
                if (x - 1 >= 0 && y + 1 < canvasHeight) {
                    data[i - 4 + canvasWidth * 4] += error * (3 / 16);
                    data[i - 3 + canvasWidth * 4] += error * (3 / 16);
                    data[i - 2 + canvasWidth * 4] += error * (3 / 16);
                }
                if (y + 1 < canvasHeight) {
                    data[i + canvasWidth * 4] += error * (5 / 16);
                    data[i + 1 + canvasWidth * 4] += error * (5 / 16);
                    data[i + 2 + canvasWidth * 4] += error * (5 / 16);
                }
                if (x + 1 < canvasWidth && y + 1 < canvasHeight) {
                    data[i + 4 + canvasWidth * 4] += error * (1 / 16);
                    data[i + 5 + canvasWidth * 4] += error * (1 / 16);
                    data[i + 6 + canvasWidth * 4] += error * (1 / 16);
                }
            } else {
                // Apply brightness threshold without dithering
                data[i] = data[i + 1] = data[i + 2] = gray < brightnessThreshold ? 0 : 255;
            }

            // Apply color inversion
            if (invertColors) {
                data[i] = 255 - data[i];
                data[i + 1] = 255 - data[i + 1];
                data[i + 2] = 255 - data[i + 2];
            }
        }
        ctx.putImageData(imageData, 0, 0);
    }

    // Event Listener for image file upload
    imageUpload.addEventListener('change', (event) => {
        const file = event.target.files[0];
        if (file) {
            showMessage('File selected: ' + file.name, 'info');
            const reader = new FileReader();
            reader.onload = (e) => {
                const img = new Image();
                img.onload = () => {
                    originalImage = img;
                    drawImageOnCanvas(originalImage);
                    generateCode();
                    showMessage('Image loaded successfully!', 'success');
                };
                img.onerror = () => {
                    showMessage('Error loading image. Please try another file.', 'error');
                    originalImage = null;
                };
                img.src = e.target.result;
            };
            reader.onerror = () => {
                showMessage('Error reading file. Please try again.', 'error');
                originalImage = null;
            };
            reader.readAsDataURL(file);
        } else {
            showMessage('No file selected.', 'info');
            originalImage = null;
        }
    });

    // Event Listener for byte array paste
    byteArrayPaste.addEventListener('input', (event) => {
        const byteString = event.target.value.trim();
        if (byteString) {
            try {
                const bytes = byteString.split(/[\s,]+/).map(byteStr => {
                    byteStr = byteStr.trim();
                    if (byteStr.startsWith('0x')) {
                        return parseInt(byteStr, 16);
                    } else {
                        return parseInt(byteStr, 10);
                    }
                }).filter(b => !isNaN(b) && b >= 0 && b <= 255);

                if (bytes.length > 0) {
                    const size = Math.ceil(Math.sqrt(bytes.length));
                    const tempCanvas = document.createElement('canvas');
                    tempCanvas.width = size;
                    tempCanvas.height = size;
                    const tempCtx = tempCanvas.getContext('2d');
                    const imageData = tempCtx.createImageData(size, size);

                    for (let i = 0; i < bytes.length; i++) {
                        const val = bytes[i];
                        imageData.data[i * 4] = val;
                        imageData.data[i * 4 + 1] = val;
                        imageData.data[i * 4 + 2] = val;
                        imageData.data[i * 4 + 3] = 255;
                    }
                    tempCtx.putImageData(imageData, 0, 0);

                    const img = new Image();
                    img.onload = () => {
                        originalImage = img;
                        drawImageOnCanvas(originalImage);
                        generateCode();
                    };
                    img.src = tempCanvas.toDataURL();
                    imageUpload.value = '';
                }
            } catch (error) {
                console.error('Error parsing byte array:', error);
                showMessage('Error: Invalid byte array format. Please enter comma or space-separated numbers (e.g., 0x00, 0xFF, 123).', 'error');
            }
        } else {
            originalImage = null;
            ctx.clearRect(0, 0, imagePreviewCanvas.width, imagePreviewCanvas.height);
        }
    });

    // Update sliders display
    widget.querySelector('#ditheringIntensity').addEventListener('input', (event) => {
        widget.querySelector('#ditheringIntensityValue').textContent = event.target.value;
        if (originalImage) {
            drawImageOnCanvas(originalImage);
            generateCode();
        }
    });

    widget.querySelector('#brightnessThreshold').addEventListener('input', (event) => {
        widget.querySelector('#thresholdValue').textContent = event.target.value;
        if (originalImage) {
            drawImageOnCanvas(originalImage);
            generateCode();
        }
    });

    // Event listeners for icon buttons
    const invertColorsBtn = widget.querySelector('#invertColorsBtn');
    const mirrorHBtn = widget.querySelector('#mirrorHBtn');
    const mirrorVBtn = widget.querySelector('#mirrorVBtn');
    const enableDitheringBtn = widget.querySelector('#enableDitheringBtn');
    const ditheringIntensityGroup = widget.querySelector('#ditheringIntensityGroup');

    invertColorsBtn.addEventListener('click', () => {
        invertColorsBtn.classList.toggle('active');
        if (originalImage) {
            drawImageOnCanvas(originalImage);
            generateCode();
        }
    });

    mirrorHBtn.addEventListener('click', () => {
        mirrorHBtn.classList.toggle('active');
        if (originalImage) {
            drawImageOnCanvas(originalImage);
            generateCode();
        }
    });

    mirrorVBtn.addEventListener('click', () => {
        mirrorVBtn.classList.toggle('active');
        if (originalImage) {
            drawImageOnCanvas(originalImage);
            generateCode();
        }
    });

    enableDitheringBtn.addEventListener('click', () => {
        enableDitheringBtn.classList.toggle('active');
        ditheringIntensityGroup.style.display = enableDitheringBtn.classList.contains('active') ? 'block' : 'none';
        if (originalImage) {
            drawImageOnCanvas(originalImage);
            generateCode();
        }
    });

    // Re-draw image when customization options change
    widget.querySelectorAll('#customization-panel select, #customization-panel input[type="number"], #customization-panel input[type="range"]').forEach(element => {
        element.addEventListener('change', () => {
            if (originalImage) {
                drawImageOnCanvas(originalImage);
                generateCode();
            }
        });
    });

    // Zoom functionality
    const zoomInBtn = widget.querySelector('#zoomInBtn');
    const zoomOutBtn = widget.querySelector('#zoomOutBtn');
    const resetZoomBtn = widget.querySelector('#resetZoomBtn');
    const canvasContainer = widget.querySelector('#canvasContainer');
    let currentZoom = 1.0;

    zoomInBtn.addEventListener('click', () => {
        currentZoom = Math.min(currentZoom + 0.1, 3.0);
        imagePreviewCanvas.style.transform = scale(${currentZoom});
        canvasContainer.style.overflow = currentZoom > 1.0 ? 'scroll' : 'hidden';
    });

    zoomOutBtn.addEventListener('click', () => {
        currentZoom = Math.max(currentZoom - 0.1, 0.5);
        imagePreviewCanvas.style.transform = scale(${currentZoom});
        canvasContainer.style.overflow = currentZoom > 1.0 ? 'scroll' : 'hidden';
    });

    resetZoomBtn.addEventListener('click', () => {
        currentZoom = 1.0;
        imagePreviewCanvas.style.transform = scale(${currentZoom});
        canvasContainer.style.overflow = 'hidden';
    });

    const outputFormat = widget.querySelector('#outputFormatMoved');
    const generatedCode = widget.querySelector('#generatedCode');
    const copyCodeBtn = widget.querySelector('#copyCodeBtn');
    const downloadCodeBtn = widget.querySelector('#downloadCodeBtn');

    // Function to get pixel data from canvas
    function getPixelData() {
        const canvasWidth = imagePreviewCanvas.width;
        const canvasHeight = imagePreviewCanvas.height;
        const imageData = ctx.getImageData(0, 0, canvasWidth, canvasHeight);
        const data = imageData.data;
        const pixels = [];

        for (let i = 0; i < data.length; i += 4) {
            pixels.push(data[i] === 255 ? 1 : 0);
        }
        return pixels;
    }

    // Function to convert pixel data to byte array
    function pixelsToByteArray(pixels, width, height) {
        const byteArray = [];
        for (let y = 0; y < height; y++) {
            for (let x = 0; x < width; x += 8) {
                let byte = 0;
                for (let i = 0; i < 8; i++) {
                    const pixelIndex = y * width + (x + i);
                    if (pixelIndex < pixels.length) {
                        byte |= (pixels[pixelIndex] << (7 - i));
                    }
                }
                byteArray.push(byte);
            }
        }
        return byteArray;
    }

    // Function to generate code based on selected format
    function generateCode() {
        if (!originalImage) {
            generatedCode.value = '// Upload an image or paste a byte array first.';
            return;
        }

        const pixels = getPixelData();
        const width = imagePreviewCanvas.width;
        const height = imagePreviewCanvas.height;
        const byteArray = pixelsToByteArray(pixels, width, height);
        const format = outputFormat.value;

        let code = '';
        const imageName = 'myImage';

        switch (format) {
            case 'arduino_cpp':
                code = generateArduinoCppCode(imageName, byteArray);
                break;
            case 'arduino_h':
                code = generateArduinoHCode(imageName, byteArray, width, height);
                break;
            case 'arduino_ino':
                code = generateArduinoInoCode(imageName, byteArray, width, height);
                break;
            case 'json':
                code = generateJsonCode(width, height, byteArray);
                break;
            case 'hex_array':
                code = generateHexArrayCode(byteArray);
                break;
            case 'base64':
                code = generateBase64Code(pixels, width, height);
                break;
            case 'pbm':
                code = generatePbmCode(pixels, width, height);
                break;
            case 'plain_bytes':
                code = generatePlainBytesCode(byteArray);
                break;
            case 'cpp_byte_array':
                code = generateCppByteArrayCode(imageName, byteArray);
                break;
            case 'python_byte_array':
                code = generatePythonByteArrayCode(imageName, byteArray);
                break;
            default:
                code = '// Select an output format.';
                break;
        }
        generatedCode.value = code;
    }

    function generateArduinoCppCode(imageName, byteArray) {
        return // ${imageName}.cpp\n#include "${imageName}.h"\n\nconst unsigned char ${imageName}_data[] PROGMEM = {\n  ${byteArray.map(b => '0x' + b.toString(16).padStart(2, '0')).join(', ')}\n};\n;
    }

    function generateArduinoHCode(imageName, byteArray, width, height) {
        return // ${imageName}.h\n#ifndef ${imageName.toUpperCase()}_H\n#define ${imageName.toUpperCase()}_H\n\n#include <Arduino.h>\n\nconst unsigned char ${imageName}_data[] PROGMEM = {\n  ${byteArray.map(b => '0x' + b.toString(16).padStart(2, '0')).join(', ')}\n};\nconst int ${imageName}_width = ${width};\nconst int ${imageName}_height = ${height};\n\n#endif // ${imageName.toUpperCase()}_H\n;
    }

    function generateArduinoInoCode(imageName, byteArray, width, height) {
        return // ${imageName}.ino\n#include <Adafruit_GFX.h>\n#include <Adafruit_SSD1306.h>\n\n// OLED dimensions\n#define SCREEN_WIDTH ${width}\n#define SCREEN_HEIGHT ${height}\n\n// Declaration for SSD1306 display connected to I2C\n#define OLED_RESET -1\nAdafruit_SSD1306 display(SCREEN_WIDTH, SCREEN_HEIGHT, &Wire, OLED_RESET);\n\nconst unsigned char ${imageName}_data[] PROGMEM = {\n  ${byteArray.map(b => '0x' + b.toString(16).padStart(2, '0')).join(', ')}\n};\n\nvoid setup() {\n  Serial.begin(9600);\n\n  if(!display.begin(SSD1306_SWITCHCAPVCC, 0x3C)) {\n    Serial.println(F("SSD1306 allocation failed"));\n    for(;;);\n  }\n\n  display.clearDisplay();\n  display.drawBitmap(0, 0, ${imageName}_data, ${width}, ${height}, 1);\n  display.display();\n}\n\nvoid loop() {\n  // Nothing to do here\n}\n;
    }

    function generateJsonCode(width, height, byteArray) {
        return JSON.stringify({
            width: width,
            height: height,
            format: 'monochrome_packed_bytes',
            data: byteArray.map(b => '0x' + b.toString(16).padStart(2, '0'))
        }, null, 2);
    }

    function generateHexArrayCode(byteArray) {
        return byteArray.map(b => '0x' + b.toString(16).padStart(2, '0')).join(', ');
    }

    function generateBase64Code(pixels, width, height) {
        const tempCanvas = document.createElement('canvas');
        tempCanvas.width = width;
        tempCanvas.height = height;
        const tempCtx = tempCanvas.getContext('2d');
        const tempImageData = tempCtx.createImageData(width, height);
        for (let i = 0; i < pixels.length; i++) {
            const val = pixels[i] === 1 ? 255 : 0;
            tempImageData.data[i * 4] = val;
            tempImageData.data[i * 4 + 1] = val;
            tempImageData.data[i * 4 + 2] = val;
            tempImageData.data[i * 4 + 3] = 255;
        }
        tempCtx.putImageData(tempImageData, 0, 0);
        return tempCanvas.toDataURL('image/png');
    }

    function generatePbmCode(pixels, width, height) {
        let code = P4\n${width} ${height}\n;
        for (let y = 0; y < height; y++) {
            for (let x = 0; x < width; x += 8) {
                let byte = 0;
                for (let i = 0; i < 8; i++) {
                    const pixelIndex = y * width + (x + i);
                    if (pixelIndex < pixels.length) {
                        byte |= ((1 - pixels[pixelIndex]) << (7 - i));
                    }
                }
                code += String.fromCharCode(byte);
            }
        }
        return code;
    }

    function generatePlainBytesCode(byteArray) {
        return byteArray.join(', ');
    }

    function generateCppByteArrayCode(imageName, byteArray) {
        return const unsigned char ${imageName}_data[] = {\n  ${byteArray.map(b => '0x' + b.toString(16).padStart(2, '0')).join(', ')}\n};;
    }

    function generatePythonByteArrayCode(imageName, byteArray) {
        return ${imageName}_data = [\n  ${byteArray.map(b => '0x' + b.toString(16).padStart(2, '0')).join(', ')}\n];
    }

    // Event listeners for output generation
    outputFormat.addEventListener('change', generateCode);

    // Alignment buttons event listeners
    widget.querySelectorAll('.alignment-buttons .align-btn').forEach(button => {
        button.addEventListener('click', function() {
            const parent = this.closest('.alignment-buttons');
            parent.querySelectorAll('.align-btn').forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            if (originalImage) {
                drawImageOnCanvas(originalImage);
                generateCode();
            }
        });
    });

    // Set initial active state for alignment buttons
    widget.querySelector('#alignH .alignment-buttons .align-btn[data-value="left"]').classList.add('active');
    widget.querySelector('#alignV .alignment-buttons .align-btn[data-value="top"]').classList.add('active');

    // Copy to clipboard functionality
    copyCodeBtn.addEventListener('click', () => {
        navigator.clipboard.writeText(generatedCode.value).then(() => {
            showMessage('Code copied to clipboard!', 'success');
        }).catch(err => {
            console.error('Failed to copy: ', err);
            showMessage('Failed to copy code!', 'error');
        });
    });

    // Download code functionality
    downloadCodeBtn.addEventListener('click', () => {
        const filename = image_data.${outputFormat.value.split('_')[0]};
        const element = document.createElement('a');
        element.setAttribute('href', 'data:text/plain;charset=utf-8,' + encodeURIComponent(generatedCode.value));
        element.setAttribute('download', filename);
        element.style.display = 'none';
        document.body.appendChild(element);
        element.click();
        document.body.removeChild(element);
    });

    function showMessage(message, type) {
        const messageArea = widget.querySelector('#message-area');
        messageArea.textContent = message;
        messageArea.style.display = 'block';
        if (type === 'success') {
            messageArea.style.backgroundColor = '#d4edda';
            messageArea.style.color = '#155724';
            messageArea.style.borderColor = '#c3e6cb';
        } else if (type === 'error') {
            messageArea.style.backgroundColor = '#f8d7da';
            messageArea.style.color = '#721c24';
            messageArea.style.borderColor = '#f5c6cb';
        } else {
            messageArea.style.backgroundColor = '#e2e3e5';
            messageArea.style.color = '#383d41';
            messageArea.style.borderColor = '#d6d8db';
        }
        setTimeout(() => {
            messageArea.style.display = 'none';
        }, 3000);
    }

    // Set initial values for range displays
    widget.querySelector('#ditheringIntensityValue').textContent = widget.querySelector('#ditheringIntensity').value;
    widget.querySelector('#thresholdValue').textContent = widget.querySelector('#brightnessThreshold').value;

    // Hide dithering intensity group initially
    if (!widget.querySelector('#enableDitheringBtn').classList.contains('active')) {
        widget.querySelector('#ditheringIntensityGroup').style.display = 'none';
    }
})();
</script>